<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>App Icon Generator</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f0f4f8; /* Light blue-gray background */
        }
        .container {
            max-width: 90%;
            margin: 0 auto;
            padding: 2rem;
        }
        .card {
            background-color: #ffffff;
            border-radius: 1.5rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1.5rem;
        }
        .input-group {
            width: 100%;
        }
        input[type="text"] {
            border: 2px solid #cbd5e1; /* Light gray border */
            border-radius: 0.75rem;
            padding: 0.75rem 1rem;
            width: 100%;
            transition: border-color 0.3s ease;
        }
        input[type="text"]:focus {
            outline: none;
            border-color: #6366f1; /* Indigo focus color */
        }
        .button-group {
            display: flex;
            gap: 1rem; /* Space between buttons */
            width: 100%;
            justify-content: center;
            flex-wrap: wrap; /* Allow buttons to wrap on smaller screens */
        }
        button {
            background-color: #6366f1; /* Indigo */
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 0.75rem;
            font-weight: 600;
            transition: background-color 0.3s ease, transform 0.2s ease;
            cursor: pointer;
            border: none;
            flex-grow: 1; /* Allow buttons to grow and fill space */
            min-width: 150px; /* Minimum width for buttons */
        }
        button:hover {
            background-color: #4f46e5; /* Darker indigo on hover */
            transform: translateY(-2px);
        }
        button:disabled {
            background-color: #a0aec0; /* Gray out disabled buttons */
            cursor: not-allowed;
            transform: none;
        }
        .image-display {
            border: 2px dashed #a0aec0; /* Gray dashed border */
            border-radius: 1rem;
            min-height: 200px;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            width: 100%;
        }
        .image-display img {
            max-width: 100%;
            height: auto;
            border-radius: 0.75rem; /* Rounded corners for the image */
        }
        .loading-spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            border-left-color: #6366f1; /* Indigo spinner */
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }
        .message-box {
            background-color: #ffeccf; /* Light yellow */
            color: #8f5c0a; /* Dark yellow text */
            border-radius: 0.75rem;
            padding: 1rem;
            width: 100%;
            text-align: center;
            font-weight: 500;
            margin-top: 1rem;
        }
    </style>
</head>
<body class="bg-gray-100 flex items-center justify-center min-h-screen">
    <div class="container">
        <div class="card">
            <h1 class="text-3xl font-bold text-gray-800 mb-4 text-center">App Icon Generator</h1>
            <div class="input-group">
                <label for="promptInput" class="block text-gray-700 text-lg font-medium mb-2">Describe your app icon:</label>
                <input type="text" id="promptInput" placeholder="e.g., A cute owl focusing on a book, vibrant colors" class="w-full">
            </div>
            <div class="button-group">
                <button id="enhancePromptButton">✨ Enhance Prompt ✨</button>
                <button id="generateButton">Generate Icon</button>
            </div>
            <div id="loading" class="hidden loading-spinner"></div>
            <div id="imageDisplay" class="image-display">
                <p id="placeholderText" class="text-gray-500">Your generated icon will appear here.</p>
            </div>
            <div id="messageBox" class="message-box hidden"></div>
        </div>
    </div>

    <script>
        const promptInput = document.getElementById('promptInput');
        const generateButton = document.getElementById('generateButton');
        const enhancePromptButton = document.getElementById('enhancePromptButton'); // New button
        const loading = document.getElementById('loading');
        const imageDisplay = document.getElementById('imageDisplay');
        const placeholderText = document.getElementById('placeholderText');
        const messageBox = document.getElementById('messageBox');

        // Function to display messages
        function showMessage(message, type = 'info') {
            messageBox.textContent = message;
            messageBox.className = `message-box ${type === 'error' ? 'bg-red-200 text-red-800' : 'bg-yellow-200 text-yellow-800'}`;
            messageBox.classList.remove('hidden');
        }

        // Function to hide messages
        function hideMessage() {
            messageBox.classList.add('hidden');
        }

        // Function to set loading state
        function setLoading(isLoading) {
            if (isLoading) {
                loading.classList.remove('hidden');
                generateButton.disabled = true;
                enhancePromptButton.disabled = true;
            } else {
                loading.classList.add('hidden');
                generateButton.disabled = false;
                enhancePromptButton.disabled = false;
            }
        }

        // Event listener for generating the image
        generateButton.addEventListener('click', async () => {
            const prompt = promptInput.value.trim();
            if (!prompt) {
                showMessage('Please enter a description for the app icon.', 'error');
                return;
            }

            // Clear previous content
            imageDisplay.innerHTML = '';
            placeholderText.classList.add('hidden');
            hideMessage();
            setLoading(true);

            try {
                // Construct the payload for the API call to imagen-4.0-generate-preview-06-06
                const payload = { instances: { prompt: prompt }, parameters: { "sampleCount": 1} };
                const apiKey = ""; // API key provided by the environment
                // Updated API URL to try imagen-4.0-generate-preview-06-06
                const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/imagen-4.0-generate-preview-06-06:predict?key=${apiKey}`;

                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(payload)
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.error.message || 'Image generation failed.');
                }

                const result = await response.json();

                if (result.predictions && result.predictions.length > 0 && result.predictions[0].bytesBase64Encoded) {
                    const imageUrl = `data:image/png;base64,${result.predictions[0].bytesBase64Encoded}`;
                    const imgElement = document.createElement('img');
                    imgElement.src = imageUrl;
                    imgElement.alt = "Generated App Icon";
                    imgElement.classList.add('w-full', 'h-full', 'object-contain');
                    imageDisplay.appendChild(imgElement);
                } else {
                    showMessage('No image data received. Please try again with a different prompt.', 'error');
                }
            } catch (error) {
                console.error('Error generating image:', error);
                showMessage(`Failed to generate icon: ${error.message}`, 'error');
            } finally {
                setLoading(false);
                if (imageDisplay.innerHTML === '') {
                    placeholderText.classList.remove('hidden');
                }
            }
        });

        // Event listener for enhancing the prompt using Gemini API
        enhancePromptButton.addEventListener('click', async () => {
            let userIdea = promptInput.value.trim();
            if (!userIdea) {
                userIdea = "fun and engaging app icon for attention span training"; // Default idea
            }

            hideMessage();
            setLoading(true);

            try {
                const chatHistory = [];
                chatHistory.push({
                    role: "user",
                    parts: [{ text: `Given the following brief idea for an attention span training app icon, elaborate it into a detailed, creative, and visually descriptive prompt suitable for an image generation AI. Focus on making it fun, engaging, and relevant to attention span improvement.
                    Idea: ${userIdea}
                    Elaborated Prompt:` }]
                });

                const payload = { contents: chatHistory };
                const apiKey = ""; // API key provided by the environment
                const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;

                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(payload)
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.error.message || 'Prompt enhancement failed.');
                }

                const result = await response.json();

                if (result.candidates && result.candidates.length > 0 &&
                    result.candidates[0].content && result.candidates[0].content.parts &&
                    result.candidates[0].content.parts.length > 0) {
                    const enhancedPrompt = result.candidates[0].content.parts[0].text;
                    promptInput.value = enhancedPrompt; // Update the input field with the enhanced prompt
                    showMessage('Prompt enhanced successfully! Now try generating an icon.', 'info');
                } else {
                    showMessage('Could not enhance prompt. Please try again.', 'error');
                }

            } catch (error) {
                console.error('Error enhancing prompt:', error);
                showMessage(`Failed to enhance prompt: ${error.message}`, 'error');
            } finally {
                setLoading(false);
            }
        });
    </script>
</body>
</html>
