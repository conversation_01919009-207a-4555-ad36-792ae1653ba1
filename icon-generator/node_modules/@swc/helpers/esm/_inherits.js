import { _set_prototype_of } from "./_set_prototype_of.js";

export function _inherits(subClass, superClass) {
    if (typeof superClass !== "function" && superClass !== null) {
        throw new TypeError("Super expression must either be null or a function");
    }

    subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } });

    if (superClass) _set_prototype_of(subClass, superClass);
}
export { _inherits as _ };
